package graphikos.automation.service;

import graphikos.automation.model.BuildRequest;
import graphikos.automation.model.User;
import graphikos.automation.repository.BuildRequestRepository;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;

@Service
public class BuildService {

    @Autowired
    private BuildRequestRepository buildRequestRepository;

    public BuildRequest createBuildRequest(BuildRequest buildRequest, User user) {
        buildRequest.setCreatedBy(user);
        buildRequest.setCreatedAt(LocalDateTime.now());
        buildRequest.setUpdatedAt(LocalDateTime.now());
        buildRequest.setStatus(BuildRequest.BuildStatus.SUBMITTED);
        
        // Set default automation configuration
        String defaultConfig = generateDefaultAutomationConfig();
        buildRequest.setAutomationConfig(defaultConfig);
        
        return buildRequestRepository.save(buildRequest);
    }

    public Optional<BuildRequest> findById(Long id) {
        return buildRequestRepository.findById(id);
    }

    public List<BuildRequest> findAllBuilds() {
        return buildRequestRepository.findAllByOrderByCreatedAtDesc();
    }

    public List<BuildRequest> findBuildsByUser(User user) {
        return buildRequestRepository.findByCreatedByOrderByCreatedAtDesc(user);
    }

    public List<BuildRequest> findBuildsByStatus(BuildRequest.BuildStatus status) {
        return buildRequestRepository.findByStatusOrderByCreatedAtDesc(status);
    }

    public BuildRequest updateBuildRequest(BuildRequest buildRequest) {
        buildRequest.setUpdatedAt(LocalDateTime.now());
        return buildRequestRepository.save(buildRequest);
    }

    public BuildRequest updateBuildStatus(Long id, BuildRequest.BuildStatus status) {
        Optional<BuildRequest> buildOpt = buildRequestRepository.findById(id);
        if (buildOpt.isPresent()) {
            BuildRequest build = buildOpt.get();
            build.setStatus(status);
            build.setUpdatedAt(LocalDateTime.now());
            return buildRequestRepository.save(build);
        }
        throw new RuntimeException("Build request not found");
    }

    public BuildRequest updateBuildRequest(BuildRequest buildRequest) {
        buildRequest.setUpdatedAt(LocalDateTime.now());
        return buildRequestRepository.save(buildRequest);
    }

    public BuildRequest startAutomation(Long id, String reportUrl) {
        Optional<BuildRequest> buildOpt = buildRequestRepository.findById(id);
        if (buildOpt.isPresent()) {
            BuildRequest build = buildOpt.get();
            build.setStatus(BuildRequest.BuildStatus.AUTOMATION_RUNNING);
            build.setAutomationReportUrl(reportUrl);
            build.setUpdatedAt(LocalDateTime.now());
            return buildRequestRepository.save(build);
        }
        throw new RuntimeException("Build request not found");
    }

    public BuildRequest updatePrechecks(Long id, String buildDiff, String changedFiles, 
                                       String zdcmFiles, String migrationFiles) {
        Optional<BuildRequest> buildOpt = buildRequestRepository.findById(id);
        if (buildOpt.isPresent()) {
            BuildRequest build = buildOpt.get();
            build.setBuildDiffContent(buildDiff);
            build.setChangedFilesContent(changedFiles);
            build.setZdcmFilesContent(zdcmFiles);
            build.setMigrationFilesContent(migrationFiles);
            build.setUpdatedAt(LocalDateTime.now());
            return buildRequestRepository.save(build);
        }
        throw new RuntimeException("Build request not found");
    }

    public long countByStatus(BuildRequest.BuildStatus status) {
        return buildRequestRepository.countByStatus(status);
    }

    public long countByUser(User user) {
        return buildRequestRepository.countByCreatedBy(user);
    }

    private String generateDefaultAutomationConfig() {
        return "#gc launch configurations\n" +
               "domainSetup=gcautomation\n" +
               "localUrl=gcautomation.localzoho.com\n" +
               "buildDetailsAPI=show/buildDetails\n" +
               "\n" +
               "#Report and account configurations\n" +
               "localServerMachineName=\n" +
               "\n" +
               "#notification configurations\n" +
               "automationStatus=automation\n" +
               "\n" +
               "#cliq bot config properties\n" +
               "tomcatPath=\n" +
               "reportSubjectForBot=Full Automation\n" +
               "sendReportInBot=yes\n" +
               "cliqReportBotURL=https://cliq.zoho.com/company/*********/api/v2/bots/showreport/incoming\n" +
               "\n" +
               "#browser configurations\n" +
               "browser=googlechrome\n" +
               "addExtension=true\n" +
               "idc=false\n" +
               "gridEnabled=false\n" +
               "enableConsoleLogs=yes\n" +
               "enableBiDiLogging=yes\n" +
               "testOpVideo=yes\n";
    }
}
